using Microsoft.AspNetCore.Mvc;
using RESServer.Models;
using RESServer.Services;
using System.Diagnostics;
using System.IO;
using System.IO.Compression;
using System.Linq;

namespace RESServer.Controllers
{
    /// <summary>
    /// 处理 DWG 文件的 API 控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class DrawingProcessingController : ControllerBase
    {
        private readonly ILogger<DrawingProcessingController> _logger;
        private readonly AccoreConsoleService _accoreConsoleService;
        private readonly IConfiguration _configuration;
        
        public DrawingProcessingController(
            ILogger<DrawingProcessingController> logger,
            AccoreConsoleService accoreConsoleService,
            IConfiguration configuration)
        {
            _logger = logger;
            _accoreConsoleService = accoreConsoleService;
            _configuration = configuration;
        }
        
        /// <summary>
        /// 上传并处理 DWG 文件，使用 accoreconsole.exe 拆分图纸
        /// </summary>
        /// <param name="file">DWG 文件</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>处理结果信息</returns>
        /// <response code="200">成功处理文件并返回结果</response>
        /// <response code="400">请求无效，例如未提供文件或文件类型不正确</response>
        /// <response code="500">服务器内部错误</response>
        [HttpPost("process")]
        [RequestFormLimits(ValueLengthLimit = int.MaxValue, MultipartBodyLengthLimit = 100_000_000)]
        [RequestSizeLimit(100_000_000)] // ~100MB
        [Produces("application/json", "application/zip")]
        [ProducesResponseType(typeof(DrawingProcessingResult), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(DrawingProcessingResult), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(DrawingProcessingResult), StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> ProcessDrawing(IFormFile file, CancellationToken cancellationToken)
        {
            if (file == null || file.Length == 0)
            {
                return BadRequest(new DrawingProcessingResult 
                { 
                    Success = false, 
                    Message = "未提供 DWG 文件" 
                });
            }
            
            if (!Path.GetExtension(file.FileName).Equals(".dwg", StringComparison.OrdinalIgnoreCase))
            {
                return BadRequest(new DrawingProcessingResult 
                { 
                    Success = false, 
                    Message = "文件类型必须是 DWG" 
                });
            }
            
            _logger.LogInformation("收到 DWG 文件上传请求: {FileName}, {Size} 字节", file.FileName, file.Length);
            
            // 创建临时文件存储上传的 DWG
            string tempFilePath = Path.GetTempFileName() + ".dwg";
            try
            {
                // 保存上传的文件
                using (var stream = new FileStream(tempFilePath, FileMode.Create))
                {
                    await file.CopyToAsync(stream, cancellationToken);
                }
                
                _logger.LogInformation("DWG 文件已保存到临时位置: {Path}", tempFilePath);
                
                // 使用 Stopwatch 测量处理时间
                var stopwatch = Stopwatch.StartNew();
                
                // 调用服务处理文件，不等待处理完成
                var (resultFilePath, sessionId) = await _accoreConsoleService.ProcessDrawingAsync(
                    tempFilePath, 
                    cancellationToken, 
                    waitForCompletion: false);
                
                stopwatch.Stop();
                _logger.LogInformation("DWG 文件上传完成，已开始处理，耗时: {ElapsedMs}ms, 会话ID: {SessionId}", 
                    stopwatch.ElapsedMilliseconds, sessionId);
                
                // 返回处理中的状态和会话 ID
                return Ok(new DrawingProcessingResult
                {
                    Success = true,
                    Message = "DWG 文件已上传，正在处理中",
                    SessionId = sessionId
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理 DWG 文件时发生错误: {FileName}", file.FileName);
                return StatusCode(500, new DrawingProcessingResult 
                { 
                    Success = false, 
                    Message = $"处理文件时出错: {ex.Message}" 
                });
            }
            finally
            {
                // 清理临时文件
                if (System.IO.File.Exists(tempFilePath))
                {
                    try
                    {
                        System.IO.File.Delete(tempFilePath);
                        _logger.LogDebug("已删除临时文件: {Path}", tempFilePath);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "删除临时文件失败: {Path}", tempFilePath);
                    }
                }
            }
        }
        
        /// <summary>
        /// 通过会话ID下载处理结果
        /// </summary>
        /// <param name="sessionId">处理会话ID</param>
        /// <returns>处理结果文件</returns>
        /// <response code="200">返回处理结果文件</response>
        /// <response code="400">请求无效，例如未提供会话ID</response>
        /// <response code="404">找不到指定会话的数据</response>
        /// <response code="500">服务器内部错误</response>
        [HttpGet("download/{sessionId}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult DownloadResult(string sessionId)
        {
            if (string.IsNullOrEmpty(sessionId))
            {
                return BadRequest("必须提供会话ID");
            }

            try
            {
                // 查找输出目录
                string accoreConsolePath = _configuration["AccoreConsole:Path"] ?? @"C:\Program Files\Autodesk\AutoCAD 2025\accoreconsole.exe";
                string printDrawingsOutput = Path.Combine(Path.GetDirectoryName(accoreConsolePath), "PrintDrawingsOutput");

                if (!Directory.Exists(printDrawingsOutput))
                {
                    _logger.LogWarning("PrintDrawingsOutput 目录不存在: {Path}", printDrawingsOutput);
                    return NotFound("找不到输出目录。");
                }

                string[] possibleDirs = Directory.GetDirectories(printDrawingsOutput, $"{sessionId}*");
                if (possibleDirs.Length == 0)
                {
                    return NotFound($"找不到会话 {sessionId} 的处理结果目录。可能仍在处理中或处理失败。");
                }

                string outputDir = possibleDirs[0];

                if (Directory.GetFiles(outputDir).Length == 0 && Directory.GetDirectories(outputDir).Length == 0)
                {
                    return NotFound($"会话 {sessionId} 的结果目录为空。");
                }

                string tempDirectory = Path.Combine(Path.GetTempPath(), "AccoreConsoleService");
                string sessionDir = Path.Combine(tempDirectory, sessionId);
                Directory.CreateDirectory(sessionDir);

                string zipPath = Path.Combine(sessionDir, $"results_{sessionId}.zip");

                if (System.IO.File.Exists(zipPath))
                {
                    System.IO.File.Delete(zipPath);
                }

                using (var archive = ZipFile.Open(zipPath, ZipArchiveMode.Create))
                {
                    var filesToZip = Directory.GetFiles(outputDir, "*", SearchOption.AllDirectories);

                    foreach (var filePath in filesToZip)
                    {
                        // 跳过任何以下划线开头的文件，确保不打包标记文件
                        if (Path.GetFileName(filePath).StartsWith("_"))
                        {
                            _logger.LogInformation("Skipping marker file during packaging: {FileName}", Path.GetFileName(filePath));
                            continue;
                        }

                        // 创建相对路径以在 ZIP 中保留目录结构
                        string entryName = Path.GetRelativePath(outputDir, filePath);
                        archive.CreateEntryFromFile(filePath, entryName);
                    }
                }

                _logger.LogInformation("已为会话 {SessionId} 创建结果包: {ZipPath}", sessionId, zipPath);

                string fileName = $"drawing_processing_results_{sessionId}_{DateTime.Now:yyyyMMdd_HHmmss}.zip";

                return PhysicalFile(zipPath, "application/zip", fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "下载会话 {SessionId} 的结果时出错", sessionId);
                return StatusCode(500, $"下载结果时出错: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 获取处理会话的状态信息
        /// </summary>
        /// <param name="sessionId">处理会话ID</param>
        /// <returns>会话状态信息</returns>
        /// <response code="200">返回会话状态信息</response>
        /// <response code="404">找不到指定会话的数据</response>
        [HttpGet("status/{sessionId}")]
        [ProducesResponseType(typeof(ProcessingStatusInfo), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public IActionResult GetSessionStatus(string sessionId)
        {
            if (string.IsNullOrEmpty(sessionId))
            {
                return BadRequest("必须提供会话ID");
            }

            try
            {
                string tempDirectory = Path.Combine(Path.GetTempPath(), "AccoreConsoleService");
                string sessionDir = Path.Combine(tempDirectory, sessionId);

                if (!Directory.Exists(sessionDir))
                {
                    return NotFound($"找不到会话 {sessionId} 的数据");
                }

                // 检查进程是否已完成
                var completionFile = Directory.GetFiles(sessionDir, "_COMPLETED_*").FirstOrDefault();
                var failureFile = Directory.GetFiles(sessionDir, "_FAILED").FirstOrDefault();

                bool isFinished = completionFile != null || failureFile != null;
                string statusMessage;

                if (failureFile != null)
                {
                    statusMessage = "处理失败。请检查服务器日志。";
                }
                else if (completionFile != null)
                {
                    var exitCodeString = Path.GetFileName(completionFile).Split('_').Last();
                    if (int.TryParse(exitCodeString, out int exitCode) && exitCode == 0)
                    {
                        statusMessage = "处理已成功完成。";
                    }
                    else
                    {
                        statusMessage = $"处理已完成，但可能存在问题 (退出代码: {exitCodeString})。";
                    }
                }
                else
                {
                    statusMessage = "正在处理中...";
                }

                // 即使进程完成，我们仍然可以获取文件信息
                long fileSize = 0;
                int fileCount = 0;
                int dwgFileCount = 0;
                int gifFileCount = 0;

                string accoreConsolePath = _configuration["AccoreConsole:Path"] ?? @"C:\Program Files\Autodesk\AutoCAD 2025\accoreconsole.exe";
                string printDrawingsOutput = Path.Combine(Path.GetDirectoryName(accoreConsolePath), "PrintDrawingsOutput");

                if (Directory.Exists(printDrawingsOutput))
                {
                    string[] possibleDirs = Directory.GetDirectories(printDrawingsOutput, $"{sessionId}*");

                    if (possibleDirs.Length > 0)
                    {
                        string outputDir = possibleDirs[0];
                        var outputFiles = Directory.GetFiles(outputDir);
                        fileCount = outputFiles.Length;
                        fileSize = outputFiles.Sum(f => new FileInfo(f).Length);
                        
                        // 统计DWG和GIF文件数量
                        dwgFileCount = outputFiles.Count(f => Path.GetExtension(f).Equals(".dwg", StringComparison.OrdinalIgnoreCase));
                        gifFileCount = outputFiles.Count(f => Path.GetExtension(f).Equals(".gif", StringComparison.OrdinalIgnoreCase));
                        
                        // 根据文件类型更新状态消息
                        if (!isFinished)
                        {
                            if (dwgFileCount > 0 && gifFileCount == 0)
                            {
                                statusMessage = "CAD数据拆分中...";
                            }
                            else if (gifFileCount > 0)
                            {
                                statusMessage = "图片转换中...";
                            }
                        }
                    }
                }

                return Ok(new ProcessingStatusInfo
                {
                    IsCompleted = isFinished,
                    CurrentOperation = statusMessage,
                    FileCount = fileCount,
                    FileSize = fileSize,
                    DwgFileCount = dwgFileCount,
                    GifFileCount = gifFileCount,
                    ResultFilePath = "" // 不再适用
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取会话 {SessionId} 状态时出错", sessionId);
                return StatusCode(500, $"获取状态时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 通过会话ID获取处理结果目录中的文件列表
        /// </summary>
        /// <param name="sessionId">处理会话ID</param>
        /// <returns>文件列表</returns>
        [HttpGet("files/{sessionId}")]
        [ProducesResponseType(typeof(DirectoryFileInfo), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public IActionResult GetSessionFiles(string sessionId)
        {
            if (string.IsNullOrEmpty(sessionId))
            {
                return BadRequest("必须提供会话ID");
            }

            try
            {
                // 查找输出目录
                string accoreConsolePath = _configuration["AccoreConsole:Path"] ?? @"C:\Program Files\Autodesk\AutoCAD 2025\accoreconsole.exe";
                string printDrawingsOutput = Path.Combine(Path.GetDirectoryName(accoreConsolePath), "PrintDrawingsOutput");

                if (!Directory.Exists(printDrawingsOutput))
                {
                    return NotFound("找不到输出目录。");
                }

                string[] possibleDirs = Directory.GetDirectories(printDrawingsOutput, $"{sessionId}*");
                if (possibleDirs.Length == 0)
                {
                    return NotFound($"找不到会话 {sessionId} 的处理结果目录。");
                }

                string outputDir = possibleDirs[0];

                var files = Directory.GetFiles(outputDir)
                    .Select(f => new FileInfo(f))
                    .Select(f => new FileDetails
                    {
                        FileName = f.Name,
                        FileSize = f.Length,
                        LastModified = f.LastWriteTimeUtc
                    })
                    .ToList();

                return Ok(files);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取会话 {SessionId} 的文件列表时出错", sessionId);
                return StatusCode(500, $"获取文件列表时出错: {ex.Message}");
            }
        }
    }
} 